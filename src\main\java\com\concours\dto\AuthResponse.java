package com.concours.dto;

import lombok.Data;

@Data
public class AuthResponse {
    private String token;
    private String type = "Bearer";
    private String username;
    private String role;

    public AuthResponse(String token, String type, String username, String role) {
        this.token = token;
        this.type = type;
        this.username = username;
        this.role = role;
    }

    public AuthResponse(String token, String username, String role) {
        this.token = token;
        this.username = username;
        this.role = role;
        this.type = "Bearer"; // Initialisation explicite
    }

    public AuthResponse() {
        this.type = "Bearer"; // Constructeur par défaut avec initialisation
    }
}