package com.concours.config;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

public class CustomMultipartResolver extends StandardServletMultipartResolver {

    @Override
    public MultipartHttpServletRequest resolveMultipart(HttpServletRequest request) throws MultipartException {
        try {
            // Essayer d'abord la résolution standard
            return super.resolveMultipart(request);
        } catch (MultipartException e) {
            // Si on a une FileCountLimitExceededException, on essaie de la contourner
            if (e.getCause() != null && 
                e.getCause().getClass().getSimpleName().contains("FileCountLimitExceeded")) {
                
                // Log l'erreur pour debug
                System.err.println("FileCountLimitExceededException détectée, tentative de contournement...");
                
                // Retourner une requête multipart vide plutôt que de faire échouer
                // Cela permettra au contrôleur de gérer l'erreur proprement
                throw new MultipartException("Trop de fichiers dans la requête. Veuillez réduire le nombre de fichiers.", e);
            }
            // Relancer l'exception si ce n'est pas le problème qu'on veut résoudre
            throw e;
        }
    }

    @Override
    public boolean isMultipart(HttpServletRequest request) {
        // Vérifier si c'est une requête multipart valide
        return super.isMultipart(request);
    }
}
