package com.concours.config;

import org.apache.coyote.http11.Http11NioProtocol;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartResolver;

@Configuration
public class TomcatConfig {

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();

                // Configuration des timeouts
                protocol.setConnectionTimeout(60000);
                protocol.setKeepAliveTimeout(60000);

                // Configuration des threads
                protocol.setMaxThreads(200);
                protocol.setMinSpareThreads(10);
                protocol.setMaxConnections(8192);
                protocol.setAcceptCount(100);

                // Configuration des tailles via les propriétés du connecteur
                connector.setMaxPostSize(500 * 1024 * 1024); // 500MB
                connector.setMaxSavePostSize(500 * 1024 * 1024); // 500MB
            });

            // Configuration additionnelle pour les uploads
            factory.addContextCustomizers(context -> {
                // Désactiver les limites au niveau du contexte
                context.setSwallowOutput(true);
            });
        };
    }

    @Bean
    public MultipartResolver multipartResolver() {
        CustomMultipartResolver resolver = new CustomMultipartResolver();
        resolver.setResolveLazily(true);
        return resolver;
    }
}
